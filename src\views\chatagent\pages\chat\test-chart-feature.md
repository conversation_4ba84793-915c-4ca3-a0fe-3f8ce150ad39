# 图表功能测试说明

## 功能描述
在getChatHistoryById详情和流式数据sendMessageWithStreaming接口中，检测toolName以_chart结尾的工具，在总结前的step-container-summary区域添加图表或表格显示。

## 实现的功能

### 1. 数据检测逻辑
- 在`handleDirectExecutionResponse`函数中检测流式数据中的_chart工具
- 在`renderHistoryRecordAsStream`函数中检测历史数据中的_chart工具
- 收集工具结果数据到`currentRound.chartData`数组中

### 2. 模板结构修改
- 在`step-container-summary`区域添加了图表显示区域
- 支持多个图表的展示
- 根据chartType自动选择VTable或echarts组件

### 3. 图表渲染逻辑
- 添加了`renderSummaryChart`函数用于渲染echarts图表
- 添加了`generateSummaryChartOption`函数生成图表配置
- 添加了`getChartTableColumns`函数处理表格列
- 添加了watcher监听数据变化自动渲染图表

## 支持的数据格式

### _chart工具返回的数据格式
```json
{
  "YFields": "total_mails",
  "chartType": "line",
  "select_data": [
    {
      "pt_month": "202503",
      "total_mails": "420",
      "行号": "1"
    }
  ],
  "XFields": "pt_month"
}
```

### 支持的图表类型
- `line`: 折线图
- `bar`: 柱状图
- `table` / `TABLE`: 表格

## 测试场景

### 1. 流式数据测试
- 发送消息触发包含_chart工具的流式响应
- 验证图表数据被正确收集
- 验证图表在summary区域正确显示

### 2. 历史数据测试
- 加载包含_chart工具的历史对话
- 验证历史图表数据被正确解析
- 验证历史图表在summary区域正确显示

### 3. 多图表测试
- 测试一个对话中包含多个_chart工具的情况
- 验证所有图表都能正确显示

### 4. 边界情况测试
- 测试无数据的情况
- 测试数据格式错误的情况
- 测试图表渲染失败的情况

## 预期效果
1. 当对话中包含以_chart结尾的工具时，在总结区域会显示相应的图表或表格
2. 图表显示在总结内容之前，有清晰的标题和边框
3. 支持多个图表同时显示
4. 图表响应窗口大小变化
5. 表格使用VTable组件，图表使用echarts组件
